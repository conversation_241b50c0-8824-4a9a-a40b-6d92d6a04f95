import { ContactFormData } from '../components/ChatbotContactForm';

interface ContactResponse {
  success: boolean;
  message: string;
  contact_id?: number;
  created_at?: string;
  error?: string;
}

interface ContactListResponse {
  contacts: ContactRecord[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

interface ContactRecord {
  id: number;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  message?: string;
  inquiry_type: string;
  preferred_contact_method: string;
  created_at: string;
  is_contacted: boolean;
}

class ContactService {
  private apiUrl: string;

  constructor() {
    this.apiUrl = `${import.meta.env.VITE_API_HOSTNAME}/api/chatbot-contacts`;
  }

  async submitContact(contactData: ContactFormData): Promise<ContactResponse> {
    try {
      const response = await fetch(`${this.apiUrl}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contactData)
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          message: data.error || 'Failed to submit contact information',
          error: data.error
        };
      }

      return {
        success: true,
        message: data.message || 'Contact information submitted successfully',
        contact_id: data.contact_id,
        created_at: data.created_at
      };
    } catch (error) {
      console.error('Contact service error:', error);
      return {
        success: false,
        message: 'Network error. Please check your connection and try again.',
        error: (error as Error).message
      };
    }
  }

  async getContacts(page: number = 1, limit: number = 50, contacted?: boolean): Promise<ContactListResponse | null> {
    try {
      let url = `${this.apiUrl}/list?page=${page}&limit=${limit}`;
      if (contacted !== undefined) {
        url += `&contacted=${contacted}`;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching contacts:', error);
      return null;
    }
  }

  async markAsContacted(contactId: number, notes?: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/${contactId}/contacted`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notes })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.success;
    } catch (error) {
      console.error('Error marking contact as contacted:', error);
      return false;
    }
  }

  // Generate a unique chat session ID
  generateChatSessionId(): string {
    return `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Validate email format
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Format phone number
  formatPhoneNumber(phone: string): string {
    // Remove all non-digit characters
    const cleaned = phone.replace(/\D/g, '');
    
    // Format as (XXX) XXX-XXXX for US numbers
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    
    // Return original if not a standard US number
    return phone;
  }
}

export default new ContactService();
export type { ContactFormData, ContactResponse, ContactListResponse, ContactRecord };
