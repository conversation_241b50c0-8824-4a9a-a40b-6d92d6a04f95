import express from 'express';
import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

// Create router
const router = express.Router();

// Initialize PostgreSQL connection
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'spirelab',
  password: process.env.DB_PASSWORD || 'your_password',
  port: parseInt(process.env.DB_PORT || '5433'),
});

// Test database connection
pool.on('connect', () => {
  console.log('Connected to PostgreSQL database for chatbot contacts');
});

pool.on('error', (err) => {
  console.error('PostgreSQL connection error:', err);
});

// POST endpoint to save contact information
router.post('/submit', async (req, res) => {
  try {
    const {
      name,
      email,
      phone,
      company,
      message,
      inquiry_type,
      preferred_contact_method,
      chat_session_id
    } = req.body;

    // Validate required fields
    if (!name || !email) {
      return res.status(400).json({ 
        error: 'Name and email are required fields' 
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ 
        error: 'Please provide a valid email address' 
      });
    }

    // Insert contact information into database
    const query = `
      INSERT INTO chatbot_contacts 
      (name, email, phone, company, message, inquiry_type, preferred_contact_method, chat_session_id)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING id, created_at
    `;

    const values = [
      name,
      email,
      phone || null,
      company || null,
      message || null,
      inquiry_type || 'general',
      preferred_contact_method || 'email',
      chat_session_id || null
    ];

    const result = await pool.query(query, values);
    const contact = result.rows[0];

    console.log(`New chatbot contact saved: ${name} (${email}) - ID: ${contact.id}`);

    return res.status(201).json({
      success: true,
      message: 'Contact information saved successfully',
      contact_id: contact.id,
      created_at: contact.created_at
    });

  } catch (error) {
    console.error('Error saving chatbot contact:', error);
    return res.status(500).json({ 
      error: 'Failed to save contact information. Please try again.' 
    });
  }
});

// GET endpoint to retrieve contacts (for admin use)
router.get('/list', async (req, res) => {
  try {
    const { page = 1, limit = 50, contacted } = req.query;
    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

    let query = `
      SELECT id, name, email, phone, company, message, inquiry_type, 
             preferred_contact_method, created_at, is_contacted
      FROM chatbot_contacts
    `;
    
    const values: any[] = [];
    
    if (contacted !== undefined) {
      query += ` WHERE is_contacted = $1`;
      values.push(contacted === 'true');
    }
    
    query += ` ORDER BY created_at DESC LIMIT $${values.length + 1} OFFSET $${values.length + 2}`;
    values.push(parseInt(limit as string), offset);

    const result = await pool.query(query, values);
    
    // Get total count
    let countQuery = 'SELECT COUNT(*) FROM chatbot_contacts';
    const countValues: any[] = [];
    
    if (contacted !== undefined) {
      countQuery += ' WHERE is_contacted = $1';
      countValues.push(contacted === 'true');
    }
    
    const countResult = await pool.query(countQuery, countValues);
    const totalCount = parseInt(countResult.rows[0].count);

    return res.json({
      contacts: result.rows,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total: totalCount,
        pages: Math.ceil(totalCount / parseInt(limit as string))
      }
    });

  } catch (error) {
    console.error('Error retrieving chatbot contacts:', error);
    return res.status(500).json({ 
      error: 'Failed to retrieve contacts' 
    });
  }
});

// PUT endpoint to mark contact as contacted
router.put('/:id/contacted', async (req, res) => {
  try {
    const { id } = req.params;
    const { notes } = req.body;

    const query = `
      UPDATE chatbot_contacts 
      SET is_contacted = true, notes = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
      RETURNING id, name, email
    `;

    const result = await pool.query(query, [notes || null, id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Contact not found' });
    }

    const contact = result.rows[0];
    console.log(`Contact marked as contacted: ${contact.name} (${contact.email})`);

    return res.json({
      success: true,
      message: 'Contact marked as contacted',
      contact: contact
    });

  } catch (error) {
    console.error('Error updating contact status:', error);
    return res.status(500).json({ 
      error: 'Failed to update contact status' 
    });
  }
});

export default router;
