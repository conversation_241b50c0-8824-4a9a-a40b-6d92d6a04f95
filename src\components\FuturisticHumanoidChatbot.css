/* Futuristic Humanoid Chatbot Styles */
:root {
  --primary-blue: var(--color-primary, #00d4ff);
  --secondary-blue: var(--color-secondary, #0099cc);
  --accent-cyan: var(--color-accent, #00ffff);
  --neural-purple: var(--color-highlight, #8b5cf6);
  --energy-green: #00ff88;
  --warning-orange: #ff6b35;
  --bg-dark: #0a0a0f;
  --bg-darker: #050508;
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --text-primary: var(--color-text-primary, #ffffff);
  --text-secondary: var(--color-text-secondary, #b3b3b3);
  --hologram-glow: 0 0 20px var(--primary-blue), 0 0 40px var(--primary-blue), 0 0 60px var(--primary-blue);
  --theme-glow: 0 0 15px var(--primary-blue), 0 0 30px var(--primary-blue), 0 0 45px var(--primary-blue);
}

/* Trigger <PERSON> */
.humanoid-chatbot-trigger {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
}

.humanoid-trigger-button {
  position: relative;
  width: 80px;
  height: 80px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--bg-dark), var(--bg-darker));
  border: 2px solid var(--primary-blue);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease, background 0.3s ease;
  box-shadow: var(--hologram-glow);
}

.humanoid-trigger-button:hover {
  transform: scale(1.1);
  box-shadow: 0 0 30px var(--primary-blue), 0 0 60px var(--primary-blue);
}

.siri-trigger-avatar {
  position: relative;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Removed old mini avatar styles - now using shader circle */

.trigger-pulse {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 50%;
  border: 2px solid var(--primary-blue);
  opacity: 0;
  animation: pulse 2s infinite;
}

/* Main Chatbot Container */
.humanoid-chatbot {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 400px;
  height: 600px;
  z-index: 1000;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transition: all 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease, background 0.3s ease;
}

.humanoid-chatbot.expanded {
  width: 500px;
  height: 700px;
}

.humanoid-container {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--bg-dark), var(--bg-darker));
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    var(--hologram-glow);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease, background 0.3s ease;
}

/* Header */
.humanoid-header {
  padding: 20px;
  border-bottom: 1px solid var(--glass-border);
  background: linear-gradient(135deg, var(--glass-bg), transparent);
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
}

.humanoid-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-blue), transparent);
}

/* Siri Avatar Container */
.siri-avatar-container {
  position: relative;
  width: 120px;
  height: 120px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* Old humanoid avatar styles removed - using shader circle now */

/* Old humanoid face/neural network styles removed - using shader circle now */

/* Status Indicators */
.status-indicators {
  position: absolute;
  bottom: -5px;
  right: -5px;
  display: flex;
  gap: 4px;
}

.status-light {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--text-secondary);
  opacity: 0.3;
  transition: all 0.3s ease;
  transition: all 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease, background 0.3s ease;
}

.status-light.listening {
  background: var(--warning-orange);
  opacity: 1;
  box-shadow: 0 0 10px var(--warning-orange);
  animation: pulse 1s infinite;
}

.status-light.processing {
  background: var(--neural-purple);
  opacity: 1;
  box-shadow: 0 0 10px var(--neural-purple);
  animation: pulse 0.5s infinite;
}

.status-light.speech-enabled {
  background: var(--energy-green);
  opacity: 1;
  box-shadow: 0 0 10px var(--energy-green);
}

/* Header Info */
.header-info {
  flex: 1;
  min-width: 0;
}

.ai-name {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 4px 0;
  background: linear-gradient(135deg, var(--primary-blue), var(--accent-cyan));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease, background 0.3s ease;
}

.ai-title {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0 0 4px 0;
  opacity: 0.8;
}

.ai-status {
  font-size: 11px;
  color: var(--primary-blue);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Header Controls */
.header-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease, background 0.3s ease;
}

.control-btn:hover {
  background: var(--primary-blue);
  color: var(--text-primary);
  transform: scale(1.05);
  box-shadow: 0 0 15px var(--primary-blue);
}

.close-btn:hover {
  background: var(--warning-orange);
  box-shadow: 0 0 15px var(--warning-orange);
}

.contact-btn:hover {
  background: var(--energy-green);
  box-shadow: 0 0 15px var(--energy-green);
}

/* Emotion states now handled by shader circle avatar */

/* Animations */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

/* Old humanoid animation keyframes removed - shader circle handles its own animations */

/* Messages Container */
.messages-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.messages-list {
  height: 100%;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.messages-list::-webkit-scrollbar {
  width: 6px;
}

.messages-list::-webkit-scrollbar-track {
  background: var(--bg-darker);
}

.messages-list::-webkit-scrollbar-thumb {
  background: var(--primary-blue);
  border-radius: 3px;
}

/* Message Styles */
.message {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  animation: messageSlide 0.3s ease-out;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 1px solid var(--glass-border);
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
}

.user-message .message-avatar {
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
  color: var(--text-primary);
}

.ai-message-avatar {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-cyan);
}

.avatar-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: conic-gradient(var(--accent-cyan), var(--primary-blue), var(--accent-cyan));
  opacity: 0.3;
  animation: rotate 3s linear infinite;
  z-index: -1;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-text {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 12px 16px;
  color: var(--text-primary);
  line-height: 1.5;
  backdrop-filter: blur(10px);
  word-wrap: break-word;
}

.user-message .message-text {
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
  border-color: var(--primary-blue);
}

.ai-message .message-text {
  background: linear-gradient(135deg, var(--glass-bg), rgba(0, 212, 255, 0.05));
  border-color: var(--accent-cyan);
}

.message-time {
  font-size: 11px;
  color: var(--text-secondary);
  margin-top: 4px;
  opacity: 0.7;
}

.user-message .message-time {
  text-align: right;
}

/* Typing Indicator */
.typing-indicator .message-text {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.typing-animation {
  display: flex;
  gap: 4px;
}

.typing-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--accent-cyan);
  animation: typingBounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }
.typing-dot:nth-child(3) { animation-delay: 0s; }

/* Input Container */
.input-container {
  padding: 20px;
  border-top: 1px solid var(--glass-border);
  background: linear-gradient(135deg, var(--glass-bg), transparent);
}

.input-wrapper {
  display: flex;
  gap: 12px;
  align-items: center;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 12px;
  backdrop-filter: blur(10px);
}

.message-input {
  flex: 1;
  border: none;
  background: transparent;
  color: var(--text-primary);
  font-size: 14px;
  outline: none;
  min-height: 20px;
  resize: none;
}

.message-input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

.input-controls {
  display: flex;
  gap: 8px;
}

.voice-btn.listening {
  background: var(--warning-orange);
  color: var(--text-primary);
  box-shadow: 0 0 15px var(--warning-orange);
  animation: pulse 1s infinite;
}

.speech-btn.enabled {
  background: var(--energy-green);
  color: var(--text-primary);
  box-shadow: 0 0 15px var(--energy-green);
}

.send-btn {
  background: var(--primary-blue);
  color: var(--text-primary);
  border: none;
  border-radius: 8px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  transition: all 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease, background 0.3s ease;
}

.send-btn:hover:not(:disabled) {
  background: var(--accent-cyan);
  transform: scale(1.05);
  box-shadow: 0 0 15px var(--accent-cyan);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Error Message */
.error-message {
  margin-top: 12px;
  padding: 12px;
  background: rgba(255, 107, 53, 0.1);
  border: 1px solid var(--warning-orange);
  border-radius: 8px;
  color: var(--warning-orange);
  font-size: 13px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-close {
  background: none;
  border: none;
  color: var(--warning-orange);
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Additional Animations */
@keyframes messageSlide {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typingBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .humanoid-chatbot {
    width: 100vw;
    height: 100vh;
    bottom: 0;
    right: 0;
    border-radius: 0;
  }
  
  .humanoid-chatbot.expanded {
    width: 100vw;
    height: 100vh;
  }
  
  .humanoid-container {
    border-radius: 0;
  }
  
  .humanoid-trigger-button {
    width: 60px;
    height: 60px;
  }
  
  .siri-trigger-avatar {
    width: 50px;
    height: 50px;
  }
}
