import express from 'express';
import cors from 'cors';
import contactRouter from './api/contact';
import showcaseRouter from './api/showcase';
import chatRouter from './api/chat';
import { initializeDatabase } from './database-postgres';

const app = express();
const port = process.env.SERVER_PORT || process.env.PORT || 3001;

app.use(cors());
app.use(express.json());
app.use('/api', contactRouter);
app.use('/api/showcase', showcaseRouter);
app.use('/api/chat', chatRouter);

// Initialize database and start server
const startServer = async () => {
  try {
    await initializeDatabase();
    console.log('Database initialized successfully');

    app.listen(port, () => {
      console.log(`Server running on port ${port}`);
    });
  } catch (error) {
    console.error('Failed to initialize database:', error);
    process.exit(1);
  }
};

startServer();
