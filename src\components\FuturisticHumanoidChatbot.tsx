import React, { useState, useEffect, useRef } from 'react';
import { X, Send, Mic, MicOff, Volume2, VolumeX, Minimize2, Maximize2, Bo<PERSON>, User } from 'lucide-react';
import chatService from '../services/chatService';
import { ttsService } from '../services/ttsService';
import { cleanTextForTTS, cleanTextForDisplay } from '../utils/textUtils';
import { ChatCompletionMessageParam } from 'openai/resources';
import ShaderCircleAvatar from './ShaderCircleAvatar';
import './FuturisticHumanoidChatbot.css';

// Speech Recognition interfaces
interface SpeechRecognitionEvent {
  results: {
    [key: number]: {
      [key: number]: {
        transcript: string;
        confidence: number;
      };
    };
  };
}

interface SpeechRecognitionErrorEvent {
  error: string;
  message?: string;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  start(): void;
  stop(): void;
  abort(): void;
  onresult: ((event: SpeechRecognitionEvent) => void) | null;
  onerror: ((event: SpeechRecognitionErrorEvent) => void) | null;
  onstart: (() => void) | null;
  onend: (() => void) | null;
}

declare global {
  interface Window {
    SpeechRecognition: new () => SpeechRecognition;
    webkitSpeechRecognition: new () => SpeechRecognition;
  }
}

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

type HumanoidEmotion = 'idle' | 'thinking' | 'speaking' | 'happy' | 'curious' | 'excited' | 'listening' | 'processing';

const FuturisticHumanoidChatbot: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSpeechEnabled, setIsSpeechEnabled] = useState(true);
  const [isListening, setIsListening] = useState(false);
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);
  const [currentEmotion, setCurrentEmotion] = useState<HumanoidEmotion>('idle');
  const [isTyping, setIsTyping] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window !== 'undefined' && (window.SpeechRecognition || window.webkitSpeechRecognition)) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognitionInstance = new SpeechRecognition();
      
      recognitionInstance.continuous = false;
      recognitionInstance.interimResults = false;
      recognitionInstance.lang = 'en-US';

      recognitionInstance.onresult = (event: SpeechRecognitionEvent) => {
        const transcript = event.results[0][0].transcript;
        setInputText(transcript);
        setIsListening(false);
        setCurrentEmotion('idle');
      };

      recognitionInstance.onerror = (event: SpeechRecognitionErrorEvent) => {
        console.error('Speech recognition error:', event.error);
        setIsListening(false);
        setCurrentEmotion('idle');
      };

      recognitionInstance.onend = () => {
        setIsListening(false);
        setCurrentEmotion('idle');
      };

      setRecognition(recognitionInstance);
    }
  }, []);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Welcome message
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage: Message = {
        id: 'welcome',
        text: 'Hello! I\'m ARIA, your advanced AI assistant from SpireLab. I\'m here to help you with all your IT needs. How can I assist you today?',
        isUser: false,
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
      setCurrentEmotion('happy');
      
      setTimeout(() => {
        setCurrentEmotion('idle');
      }, 3000);
    }
  }, [isOpen]);

  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText.trim(),
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);
    setCurrentEmotion('thinking');
    setIsTyping(true);

    try {
      const chatMessages: ChatCompletionMessageParam[] = messages.map(msg => ({
        role: msg.isUser ? 'user' : 'assistant',
        content: msg.text
      }));

      chatMessages.push({
        role: 'user',
        content: userMessage.text
      });

      const response = await chatService.sendMessage(chatMessages);
      
      if (response.error) {
        throw new Error(response.error);
      }
      
      const cleanedResponse = cleanTextForDisplay(response.message);

      // Simulate typing effect
      setTimeout(() => {
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: cleanedResponse,
          isUser: false,
          timestamp: new Date()
        };

        setMessages(prev => [...prev, assistantMessage]);
        setIsLoading(false);
        setIsTyping(false);
        setCurrentEmotion('speaking');

        // Text-to-speech
        if (isSpeechEnabled) {
          const ttsText = cleanTextForTTS(cleanedResponse);
          ttsService.speak(ttsText);
        }

        setTimeout(() => {
          setCurrentEmotion('idle');
        }, 2000);
      }, 1000);

    } catch (error) {
      console.error('Error sending message:', error);
      setError('Failed to send message. Please try again.');
      setIsLoading(false);
      setIsTyping(false);
      setCurrentEmotion('idle');
    }
  };

  const handleVoiceInput = () => {
    if (!recognition) return;

    if (isListening) {
      recognition.stop();
      setIsListening(false);
      setCurrentEmotion('idle');
    } else {
      recognition.start();
      setIsListening(true);
      setCurrentEmotion('listening');
    }
  };

  const toggleSpeech = () => {
    setIsSpeechEnabled(!isSpeechEnabled);
    if (!isSpeechEnabled) {
      ttsService.stop();
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getEmotionClass = () => {
    switch (currentEmotion) {
      case 'thinking': return 'thinking';
      case 'speaking': return 'speaking';
      case 'happy': return 'happy';
      case 'excited': return 'excited';
      case 'curious': return 'curious';
      case 'listening': return 'listening';
      case 'processing': return 'processing';
      default: return 'idle';
    }
  };

  if (!isOpen) {
    return (
      <div className="humanoid-chatbot-trigger">
        <button
          onClick={() => setIsOpen(true)}
          className="humanoid-trigger-button"
          aria-label="Open AI Assistant"
        >
          <div className="shader-trigger-avatar">
            <ShaderCircleAvatar
              isListening={false}
              isSpeaking={false}
              isThinking={false}
              emotion="idle"
              size={60}
            />
          </div>
          <div className="trigger-pulse"></div>
        </button>
      </div>
    );
  }

  return (
    <div className={`humanoid-chatbot ${isExpanded ? 'expanded' : ''}`}>
      <div className="humanoid-container" ref={chatContainerRef}>
        {/* Header with Shader Circle Avatar */}
        <div className="humanoid-header">
          <div className="shader-avatar-container">
            <ShaderCircleAvatar
              isListening={isListening}
              isSpeaking={isSpeechEnabled && !isLoading && !isTyping}
              isThinking={isLoading || isTyping}
              emotion={currentEmotion}
              size={120}
            />

            {/* Status Indicators */}
            <div className="status-indicators">
              <div className={`status-light ${isListening ? 'listening' : ''}`}></div>
              <div className={`status-light ${isLoading ? 'processing' : ''}`}></div>
              <div className={`status-light ${isSpeechEnabled ? 'speech-enabled' : ''}`}></div>
            </div>
          </div>
          
          {/* Header Info */}
          <div className="header-info">
            <h3 className="ai-name">ARIA</h3>
            <p className="ai-title">Advanced Responsive Intelligence Assistant</p>
            <div className="ai-status">
              {isLoading ? 'Processing...' : 
               isListening ? 'Listening...' : 
               isTyping ? 'Typing...' : 'Online'}
            </div>
          </div>
          
          {/* Header Controls */}
          <div className="header-controls">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="control-btn"
              aria-label={isExpanded ? 'Minimize' : 'Maximize'}
            >
              {isExpanded ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
            </button>
            <button
              onClick={() => setIsOpen(false)}
              className="control-btn close-btn"
              aria-label="Close"
            >
              <X size={16} />
            </button>
          </div>
        </div>

        {/* Messages Area */}
        <div className="messages-container">
          <div className="messages-list">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`message ${message.isUser ? 'user-message' : 'ai-message'}`}
              >
                <div className="message-avatar">
                  {message.isUser ? (
                    <User size={20} />
                  ) : (
                    <div className="ai-message-avatar">
                      <Bot size={20} />
                      <div className="avatar-glow"></div>
                    </div>
                  )}
                </div>
                <div className="message-content">
                  <div className="message-text">{message.text}</div>
                  <div className="message-time">
                    {message.timestamp.toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </div>
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="message ai-message typing-indicator">
                <div className="message-avatar">
                  <div className="ai-message-avatar">
                    <Bot size={20} />
                    <div className="avatar-glow"></div>
                  </div>
                </div>
                <div className="message-content">
                  <div className="typing-animation">
                    <div className="typing-dot"></div>
                    <div className="typing-dot"></div>
                    <div className="typing-dot"></div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Input Area */}
        <div className="input-container">
          <div className="input-wrapper">
            <input
              ref={inputRef}
              type="text"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message..."
              className="message-input"
              disabled={isLoading}
            />
            
            <div className="input-controls">
              <button
                onClick={handleVoiceInput}
                className={`control-btn voice-btn ${isListening ? 'listening' : ''}`}
                aria-label={isListening ? 'Stop listening' : 'Start voice input'}
                disabled={!recognition}
              >
                {isListening ? <MicOff size={18} /> : <Mic size={18} />}
              </button>
              
              <button
                onClick={toggleSpeech}
                className={`control-btn speech-btn ${isSpeechEnabled ? 'enabled' : ''}`}
                aria-label={isSpeechEnabled ? 'Disable speech' : 'Enable speech'}
              >
                {isSpeechEnabled ? <Volume2 size={18} /> : <VolumeX size={18} />}
              </button>
              
              <button
                onClick={handleSendMessage}
                className="send-btn"
                disabled={!inputText.trim() || isLoading}
                aria-label="Send message"
              >
                <Send size={18} />
              </button>
            </div>
          </div>
          
          {error && (
            <div className="error-message">
              {error}
              <button onClick={() => setError(null)} className="error-close">×</button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FuturisticHumanoidChatbot;
